#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AI管理器传递修复
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_manager_passing():
    """测试AI管理器传递"""
    print("=== 测试AI管理器传递修复 ===")
    
    try:
        # 1. 启用AI功能
        print("1. 启用AI功能...")
        from smartvault.utils.config import save_ai_status, get_ai_status
        save_ai_status(True)
        print(f"AI状态: {get_ai_status()}")
        
        # 2. 创建模拟的主窗口和AI管理器
        print("2. 创建模拟主窗口...")
        from PySide6.QtWidgets import QApplication, QMainWindow
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.services.tag_service import TagService
        from smartvault.services.auto_tag_service import AutoTagService
        from smartvault.utils.config import load_config
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = QMainWindow()
        
        # 创建并初始化AI管理器
        config = load_config()
        tag_service = TagService()
        auto_tag_service = AutoTagService()
        auto_tag_service.load_rules_from_config(config)
        
        ai_manager = AIManager()
        success = ai_manager.initialize(
            config=config,
            tag_service=tag_service,
            auto_tag_service=auto_tag_service,
            db=None
        )
        
        # 将AI管理器附加到主窗口
        main_window.ai_manager = ai_manager
        print(f"AI管理器初始化: {success}")
        print(f"AI管理器可用: {ai_manager.is_available()}")
        
        # 3. 测试设置对话框的AI管理器获取
        print("3. 测试设置对话框...")
        from smartvault.ui.dialogs.settings_dialog import SettingsDialog
        
        dialog = SettingsDialog(main_window)
        
        # 测试获取AI管理器
        retrieved_ai_manager = dialog._get_ai_manager()
        print(f"设置对话框获取AI管理器: {retrieved_ai_manager is not None}")
        print(f"AI管理器是同一个实例: {retrieved_ai_manager is ai_manager}")
        
        # 4. 测试AI设置页面
        print("4. 测试AI设置页面...")
        ai_page = dialog.pages.get('ai')
        if ai_page:
            # 检查AI配置管理器是否有AI管理器引用
            has_ai_manager = ai_page.ai_config_manager._ai_manager is not None
            print(f"AI设置页面有AI管理器引用: {has_ai_manager}")
            
            if has_ai_manager:
                # 测试AI功能测试
                success, message = ai_page.ai_config_manager.test_ai_function()
                print(f"AI功能测试结果: {success}")
                print(f"测试消息: {message}")
            else:
                print("❌ AI设置页面没有AI管理器引用")
        else:
            print("❌ 找不到AI设置页面")
        
        print("=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ai_manager_passing()
