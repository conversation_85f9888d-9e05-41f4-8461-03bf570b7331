"""
高级设置页面
从原 settings_dialog.py 中的 create_advanced_tab 和部分 create_ui_tab 方法迁移而来
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QGroupBox, QLabel, QComboBox,
    QCheckBox, QSpinBox
)
from typing import Tuple
from ..base.base_page import BaseSettingsPage


class AdvancedSettingsPage(BaseSettingsPage):
    """高级设置页面"""

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)

        # 日志设置组
        logging_group = QGroupBox("日志设置")
        logging_layout = QVBoxLayout(logging_group)

        # 启用日志
        self.enable_logging_check = QCheckBox("启用日志记录")
        logging_layout.addWidget(self.enable_logging_check)

        # 日志级别
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(QLabel("日志级别:"))

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        log_level_layout.addWidget(self.log_level_combo)
        log_level_layout.addStretch()

        logging_layout.addLayout(log_level_layout)

        # 日志文件大小限制
        log_size_layout = QHBoxLayout()
        log_size_layout.addWidget(QLabel("日志文件大小限制 (MB):"))

        self.log_size_spin = QSpinBox()
        self.log_size_spin.setRange(1, 100)
        self.log_size_spin.setSingleStep(1)
        log_size_layout.addWidget(self.log_size_spin)
        log_size_layout.addStretch()

        logging_layout.addLayout(log_size_layout)

        layout.addWidget(logging_group)

        # AI功能设置组
        ai_group = QGroupBox("AI功能")
        ai_layout = QVBoxLayout(ai_group)

        # AI功能总开关
        self.enable_ai_features_check = QCheckBox("启用AI功能")
        self.enable_ai_features_check.setToolTip(
            "启用AI功能，包括智能标签建议、项目识别、系列检测等。\n"
            "关闭此选项将禁用所有AI相关功能。"
        )
        ai_layout.addWidget(self.enable_ai_features_check)

        # AI功能说明
        ai_info_label = QLabel(
            "💡 AI功能包括：智能标签建议、项目识别、系列检测、行为学习等。\n"
            "详细设置请前往\"AI功能\"设置页面。"
        )
        ai_info_label.setStyleSheet("color: #666666; font-size: 11px; margin-left: 20px;")
        ai_info_label.setWordWrap(True)
        ai_layout.addWidget(ai_info_label)

        layout.addWidget(ai_group)

        layout.addStretch()

    def load_settings(self, config: dict):
        """从配置加载设置到UI控件

        Args:
            config: 配置字典
        """
        self.config = config
        advanced_config = config.get("advanced", {})

        # 日志设置
        self.enable_logging_check.setChecked(advanced_config.get("enable_logging", True))
        log_level = advanced_config.get("log_level", "INFO")
        log_level_map = {"DEBUG": 0, "INFO": 1, "WARNING": 2, "ERROR": 3}
        self.log_level_combo.setCurrentIndex(log_level_map.get(log_level, 1))
        self.log_size_spin.setValue(advanced_config.get("log_file_size_mb", 10))

        # AI功能设置
        self.enable_ai_features_check.setChecked(advanced_config.get("enable_ai_features", False))

    def save_settings(self) -> dict:
        """从UI控件保存设置到配置

        Returns:
            dict: 高级设置字典
        """
        log_level_map = {0: "DEBUG", 1: "INFO", 2: "WARNING", 3: "ERROR"}

        ai_enabled = self.enable_ai_features_check.isChecked()

        # 同步AI设置中的启用状态
        from smartvault.utils.config import load_config, save_config
        config = load_config()
        if 'ai' not in config:
            config['ai'] = {}
        config['ai']['enabled'] = ai_enabled
        save_config(config)

        return {
            "enable_logging": self.enable_logging_check.isChecked(),
            "log_level": log_level_map[self.log_level_combo.currentIndex()],
            "log_file_size_mb": self.log_size_spin.value(),
            "enable_ai_features": ai_enabled
        }

    def validate_settings(self) -> Tuple[bool, str]:
        """验证设置有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 验证日志文件大小
        if self.log_size_spin.value() < 1:
            return False, "日志文件大小不能少于1MB"

        return True, ""

    def reset_to_defaults(self):
        """重置为默认设置"""
        self.enable_logging_check.setChecked(True)
        self.log_level_combo.setCurrentIndex(1)  # INFO
        self.log_size_spin.setValue(10)
        self.enable_ai_features_check.setChecked(False)  # AI功能默认关闭

    def get_page_title(self) -> str:
        """获取页面标题

        Returns:
            str: 页面标题
        """
        return "高级设置"
