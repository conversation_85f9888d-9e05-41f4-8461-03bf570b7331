#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI开关统一测试脚本
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_switch():
    """测试AI开关统一逻辑"""
    print("=== AI开关统一测试 ===")
    
    try:
        # 1. 测试配置加载
        print("1. 测试配置加载...")
        from smartvault.utils.config import load_config, save_ai_status, get_ai_status
        
        # 设置AI开关启用
        save_ai_status(True)
        ai_status = get_ai_status()
        print(f"✅ AI状态: {ai_status}")
        
        # 2. 测试AI管理器初始化
        print("2. 测试AI管理器初始化...")
        from smartvault.services.ai.ai_manager import AIManager
        from smartvault.services.tag_service import TagService
        from smartvault.services.auto_tag_service import AutoTagService
        
        config = load_config()
        print(f"配置中AI状态: {config.get('ai', {}).get('enabled', False)}")
        
        # 初始化服务
        tag_service = TagService()
        auto_tag_service = AutoTagService()
        auto_tag_service.load_rules_from_config(config)
        
        # 初始化AI管理器
        ai_manager = AIManager()
        success = ai_manager.initialize(
            config=config,
            tag_service=tag_service,
            auto_tag_service=auto_tag_service,
            db=None
        )
        
        print(f"✅ AI管理器初始化: {success}")
        print(f"✅ AI管理器可用: {ai_manager.is_available()}")
        
        # 3. 测试AI功能
        if ai_manager.is_available():
            print("3. 测试AI标签建议...")
            test_file = {
                'name': 'test.py',
                'extension': '.py',
                'path': '/test/test.py'
            }
            suggestions = ai_manager.suggest_tags(test_file)
            print(f"✅ AI标签建议: {suggestions}")
            
            status = ai_manager.get_status()
            print(f"✅ AI状态详情: {status}")
        else:
            print("❌ AI管理器不可用")
            
        # 4. 测试关闭AI
        print("4. 测试关闭AI...")
        save_ai_status(False)
        ai_status_off = get_ai_status()
        print(f"✅ AI关闭状态: {ai_status_off}")
        
        print("=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_ai_switch()
